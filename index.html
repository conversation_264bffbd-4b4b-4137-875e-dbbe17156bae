<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu Icons Demo - Visual Match</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        h1 {
            text-align: center;
            margin-bottom: 3rem;
            color: #1e293b;
            font-size: 2.5rem;
            font-weight: 700;
        }

        .sections-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1e293b;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 0.5rem;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .menu-item:hover {
            background-color: #f1f5f9;
        }

        .menu-item.active {
            background-color: #dbeafe;
            color: #1d4ed8;
        }

        .icon {
            width: 20px;
            height: 20px;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        .icon svg {
            width: 100%;
            height: 100%;
            fill: currentColor;
        }

        .menu-text {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .submenu-indicator {
            margin-left: auto;
            width: 16px;
            height: 16px;
            opacity: 0.5;
        }

        .dark-theme {
            background-color: #1e293b;
            color: #e2e8f0;
        }

        .dark-theme .section {
            background: #334155;
            border-color: #475569;
        }

        .dark-theme .section-title {
            color: #f1f5f9;
            border-color: #475569;
        }

        .dark-theme .menu-item:hover {
            background-color: #475569;
        }

        .theme-toggle {
            position: fixed;
            top: 2rem;
            right: 2rem;
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }

        .theme-toggle:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 Dark Mode</button>
    
    <div class="container">
        <h1>Menu Icons Demo - Visual Match</h1>
        
        <div class="sections-grid">
            <!-- Time Cards Section -->
            <div class="section">
                <h2 class="section-title">Time Cards</h2>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M9 9h6v6H9z" fill="currentColor"/>
                            <path d="M9 3v18M15 3v18M3 9h18M3 15h18" stroke="currentColor" stroke-width="1" opacity="0.3"/>
                        </svg>
                    </div>
                    <span class="menu-text">View All</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <span class="menu-text">Pending (119)</span>
                </div>
            </div>

            <!-- Schedule Section -->
            <div class="section">
                <h2 class="section-title">Schedule</h2>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M16 2v4M8 2v4M3 10h18" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">View Schedule</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M8 9h8M8 13h6" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">Shift Requests</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M16 2v4M8 2v4M3 10h18" stroke="currentColor" stroke-width="2"/>
                            <path d="M8 14l2 2 4-4" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <span class="menu-text">Availability</span>
                </div>
            </div>

            <!-- Payroll Section -->
            <div class="section">
                <h2 class="section-title">Payroll</h2>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">Company Details</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">People</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <rect x="2" y="3" width="20" height="14" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M8 21h8M12 17v4" stroke="currentColor" stroke-width="2"/>
                            <path d="M6 7h12M6 11h8" stroke="currentColor" stroke-width="1.5"/>
                        </svg>
                    </div>
                    <span class="menu-text">Payrolls</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M2 17l10 5 10-5M2 12l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">Tax Deposits</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M14 2v6h6M16 13H8M16 17H8M10 9H8" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">Documents</span>
                    <div class="submenu-indicator">
                        <svg viewBox="0 0 24 24">
                            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <span class="menu-text">Filling Authorizations</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M14 2v6h6M16 13H8M16 17H8M10 9H8" stroke="currentColor" stroke-width="2"/>
                            <circle cx="18" cy="6" r="3" fill="currentColor"/>
                        </svg>
                    </div>
                    <span class="menu-text">Tax Documents</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M3 3v18h18" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <span class="menu-text">Reports</span>
                    <div class="submenu-indicator">
                        <svg viewBox="0 0 24 24">
                            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <polygon points="5,3 19,12 5,21" fill="currentColor"/>
                        </svg>
                    </div>
                    <span class="menu-text">Run Payroll</span>
                </div>
            </div>

            <!-- Reports Section -->
            <div class="section">
                <h2 class="section-title">Reports</h2>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <span class="menu-text">Hours Report By</span>
                    <div class="submenu-indicator">
                        <svg viewBox="0 0 24 24">
                            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M3 3v18h18" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <span class="menu-text">Hours Summary</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M16 2v4M8 2v4M3 10h18" stroke="currentColor" stroke-width="2"/>
                            <circle cx="8" cy="16" r="2" fill="currentColor"/>
                        </svg>
                    </div>
                    <span class="menu-text">Daily Hours</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M3 12h1m16-8v1m1 16h-1M4 4l1 1m14 14l-1-1M4 20l1-1m14-14l-1 1" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">In/Out Activity</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <rect x="2" y="3" width="20" height="14" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M8 21h8M12 17v4" stroke="currentColor" stroke-width="2"/>
                            <path d="M7 7h10M7 11h6" stroke="currentColor" stroke-width="1.5"/>
                        </svg>
                    </div>
                    <span class="menu-text">PTO Summary</span>
                </div>
            </div>

            <!-- Settings Section -->
            <div class="section">
                <h2 class="section-title">Settings</h2>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="2" fill="none"/>
                        </svg>
                    </div>
                    <span class="menu-text">Account Settings</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="2"/>
                            <circle cx="18" cy="8" r="3" fill="currentColor"/>
                        </svg>
                    </div>
                    <span class="menu-text">Administrators</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <rect x="2" y="3" width="20" height="14" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M8 21h8M12 17v4" stroke="currentColor" stroke-width="2"/>
                            <circle cx="12" cy="10" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M12 7v6" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">Billing</span>
                </div>
            </div>

            <!-- Employee Section -->
            <div class="section">
                <h2 class="section-title">Employee</h2>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M16 2v4M8 2v4M3 10h18" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">Time Off Calendar</span>
                </div>
                <div class="menu-item">
                    <div class="icon">
                        <svg viewBox="0 0 24 24">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="2" fill="none"/>
                            <path d="M14 2v6h6M16 13H8M16 17H8M10 9H8" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <span class="menu-text">Time Off Summary</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            document.body.classList.toggle('dark-theme');
            const button = document.querySelector('.theme-toggle');
            if (document.body.classList.contains('dark-theme')) {
                button.textContent = '☀️ Light Mode';
            } else {
                button.textContent = '🌙 Dark Mode';
            }
        }

        // Add click handlers for menu items
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                // Remove active class from all items
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                // Add active class to clicked item
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
